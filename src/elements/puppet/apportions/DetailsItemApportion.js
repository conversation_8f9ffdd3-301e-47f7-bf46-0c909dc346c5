/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/11 下午3:02.
 */
import React, { Fragment, PureComponent } from 'react'
import styles from './DetailsItemApportion.module.less'
import classNames from 'classnames'
import { Button, Tooltip } from '@hose/eui'
import { OutlinedDirectionRight } from '@hose/eui-icons'
import Money from '../../../elements/puppet/Money'
import { get, isEqual } from 'lodash'
import ExceedStandardRiskTip from '../../../components/layout/ExceedStandardRiskForField'
import { EnhanceConnect } from '@ekuaibao/store'
import { detailsData, formatApportionRemarkData } from './utils'
import { app as api } from '@ekuaibao/whispered'
import { getContentByLocale } from '@ekuaibao/lib/lib/i18n-helpers'



@EnhanceConnect(state => ({
  dimensionFullPathMapForApportion: state['@common'].dimensionFullPathMapForApportion
}))
export default class DetailsItemApportion extends PureComponent {

  state = { data: [], unvisibleCount: 0, expandIndex: '' }

  componentDidMount() {
    this.updateDataSource(this.props)
  }

  componentWillReceiveProps(nextProps) {
    // if (!isEqual(this.props.dataSource, nextProps.dataSource)) {
    //   this.updateDataSource(nextProps)
    // }
    if (this.props.dataSource !== nextProps.dataSource) {
      this.updateDataSource(nextProps)
    }
  }

  // componentWillUnmount() {
  //   // 组件销毁时，清掉reducer中的自定义档案的全路径
  //   api.invokeService('@common:save:dimension:fullPathMap')
  // }

  updateDataSource = props => {
    let { dataSource = [], apportionVisibleList = [], isModify, isEditable, showAllFeeType, isCopyDetail } = props
    let list = detailsData(dataSource)
    let { data, fieldIds = [] } = list
    let unvisibleCount = data.length
    if ((isModify || !isEditable) && !showAllFeeType) {
      // 复制明细时暂不根据明细可见性过滤分摊
      data = data.filter(el => !el.apportionId || apportionVisibleList.includes(el.apportionId) || isCopyDetail)
      unvisibleCount -= data.length
    } else {
      unvisibleCount = 0
    }
    // if (fieldIds.length > 0) {
    //   // api.invokeService('@common:get:dimensionItems:fullName', fieldIds).then(result => {
    //   api.invokeService('@common:get:dimensionItems:fullName:byId', fieldIds).then(result => {
    //     let map = {}
    //     result.forEach(item => {
    //       map[item.id] = item.fullName
    //     })
    //     // api.invokeService('@common:save:dimension:fullPathMap', map)
    //     this.setState({ data, fullNameMap: map, unvisibleCount })
    //   })
    // } else {
    //   this.setState({ data, unvisibleCount })
    // }
    this.setState({ data, unvisibleCount })
  }
  renderItem = (line, key, external, isForbid, apportionRemarkData) => {
    const id = line.apportionId
    const validExternal = get(external, `${id}.apportionMoney`)
    const { expandIndex } = this.state
    return (
      <Fragment key={key}>
        <div className="cost-share-item">
          <div className="cost-share-item-content">
            <div className="point" />
            <div className='cost-share-item-info'>
              <span>{this.renderTitle(line.titles)}</span>
              <span>{i18n.get(`（{__k0}%）`, { __k0: Number(line.apportionPercent).toFixed(2) })}</span>
            </div>
          </div>
          <div className="money-risk-warning">
            <ExceedStandardRiskTip
              noColon={true}
              placement="left"
              style={{ display: 'flex' }}
              external={validExternal}
              isForbid={isForbid}
            >
              {line?.apportionMoney?.foreignStrCode && (
                <div className="dis-f jc-e quote-currency">
                  <span className="mr-5">
                    {/* <span className="mr-5">{i18n.get('原币')}</span> */}
                    {line.apportionMoney.foreignStrCode}
                  </span>
                  <Money value={Number(line.apportionMoney.foreign)} showSymbol={false} />
                </div>
              )}
              <div className={classNames('dis-f', 'jc-e', line?.apportionMoney?.foreignStrCode ? 'exchanged-base-currency' : 'base-currency')}>
                <span className="mr-5">
                  {line?.apportionMoney?.foreignStrCode && <span className="mr-5">{i18n.get('折合')}</span>}
                  {line.apportionMoney.standardStrCode}
                </span>
                <Money value={line.apportionMoney} showSymbol={false} />
              </div>
              {line?.apportionMoney?.budgetStrCode && (
                <div className="dis-f jc-e">
                  <span className="mr-5">
                    <span className="mr-5">{i18n.get('预算币')}</span>
                    {line.apportionMoney.budgetStrCode}
                  </span>
                  <Money value={line.apportionMoney.budget} showSymbol={false} />
                </div>
              )}
            </ExceedStandardRiskTip>
            {apportionRemarkData && apportionRemarkData.length ? (
              <div
                className="expand-btn"
                onClick={e => {
                  e.stopPropagation()
                  if (key === expandIndex) {
                    this.setState({ expandIndex: '' })
                    return
                  }
                  this.setState({ expandIndex: key })
                }}
              >
                {expandIndex === key ? i18n.get('收起') : i18n.get('展开')}
              </div>
            ) : null}
          </div>
        </div>
        <div className="item-expansion" style={{ maxHeight: `${expandIndex === key ? '200px' : '0'}` }}>
          {apportionRemarkData?.map((remark, index) => (
            <div className="item-remark" key={remark.label}>
              {remark.label}：{remark.value}
            </div>
          ))}
        </div>
      </Fragment>
    )
  }

  renderTitle = titles => {
    if (!titles) return null
    const { dimensionFullPathMapForApportion: fullNameMap } = this.props
    return titles && titles.map((line, key) => {
      const fullPathStr = line.fullPath
        ? `${line.fullPath};`
        : fullNameMap && fullNameMap[line.fieldId]
          ? `${fullNameMap[line.fieldId]};`
          : ''
      const msg = `${line.label};${fullPathStr}${line.code}`
      const name = getContentByLocale(line, 'name');
      const showName = line.active === false ? `${name}(已停用);` : `${name};`
      return (
        <span key={key}>
          <Tooltip placement="top" trigger='hover' title={msg}>
            <span style={line && line.active === false ? { color: `rgb(245, 34, 45)` } : {}}>{showName}</span>
          </Tooltip>
        </span>
      )
    })
  }

  renderTotal = (list = []) => {
    const { unvisibleCount } = this.state
    const unvisibleCountText = i18n.get(`（还有{__k0}条无可见权限）`, { __k0: unvisibleCount })
    return (
      <div className="total">
        <span>
          {i18n.get('总计')}
          {i18n.get('：')}
        </span>
        <span>
          {list.length} {i18n.get('条')}
        </span>
        {unvisibleCount > 0 && <span>{unvisibleCountText}</span>}
      </div>
    )
  }

  render() {
    let { external, showAll, isForbid, dataSource } = this.props
    const { data } = this.state
    //当大于5条时，显示更多
    let list = showAll ? data : data.length > 5 ? data.slice(0, 5) : data
    let className = showAll ? styles['cost-share'] : styles['cost-share'] + ' ' + styles['cost-share-padding']
    const txt = `${i18n.get('根据')}「${list[0]?.apportionLabel}」${i18n.get('分摊')}`
    const apportionRemarkData = dataSource.map(item => formatApportionRemarkData(item))

    return (
      <div className={className}>
        {
          !showAll && list.length > 0 && <>
            <div className="line-top" />
            <div className="title">
              <span className='text'>{txt}</span>
              {this.renderTotal(data)}
            </div>
           </>
        }
        {list.map((line, key) => this.renderItem(line, key, external, isForbid, apportionRemarkData[key]))}
        {!showAll && data.length > 5 && (
          <Button category='text' className="more">
            <span className="label">{i18n.get('更多')}</span>
            <OutlinedDirectionRight fontSize={12} />
          </Button>
        )}
      </div>
    )
  }
}
