/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/14 下午7:09.
 */
import { cloneDeep, get } from 'lodash'
import { app as api } from '@ekuaibao/whispered'
import { toJS } from 'mobx'
import moment from 'moment'
import { formatDateTime } from './../../../components/utils/fnPredefine4Date'
import { getDisplayName, getStaffName } from "../../utilFn";

export function addFullPathInApportion(dataSource = []) {
  let detailArray = cloneDeep(toJS(dataSource)) || []
  let baseDataPropertiesMap = api.getState('@common.globalFields.baseDataPropertiesMap') || {}

  let fieldIds = []
  detailArray.forEach(detail => {
    const apportions = get(detail, 'feeTypeForm.apportions', [])
    if (!apportions.length) return detail
    detail.feeTypeForm.apportions = apportions.map(line => {
      let commponents = line.specificationId.components || []
      let { apportionForm } = line
      let fieldValue
      commponents.map(item => {
        fieldValue = apportionForm[item.field]
        if (fieldValue && fieldValue.name) {
          if (baseDataPropertiesMap[item.field] &&
              get(baseDataPropertiesMap[item.field], 'dataType.type') === 'ref' &&
              get(baseDataPropertiesMap[item.field], 'dataType.entity') !== 'organization.Department') {
            fieldIds.push(fieldValue.id)
          }
        }
      })
    })
  })
  return fieldIds
}

const isUserField = (field = {}) => {
  // 草稿态拿到的是完整的用户数据，
  // 编辑态是部分数据
  return !!field.userId || field.isStaff
}

export function detailsData(dataSource = []) {
  const ds = toJS(dataSource)
  let tempDataSource = cloneDeep(ds) || []
  let data = []
  let departFullPathMap = api.getState('@common.department.noRootPathMap') || {}
  let departFullEnPathMap = api.getState('@common.department.noRootEnPathMap') || {}
  let baseDataPropertiesMap = api.getState('@common.globalFields.baseDataPropertiesMap') || {}
  // let glbF = api.getState('@common.globalFields.data')
  let projectFullPahtMap = {}
  let fieldIds = []
  tempDataSource.map(line => {
    // const labelName = line.specificationId?.configs?.find(v => v.ability === 'apportion')?.apportionMoneyField
    // const { label = '' } = glbF.find(v => v.name === labelName) || {}
    const labelName =
      i18n?.currentLocale === 'en-US' && line.specificationId?.enName
        ? line.specificationId?.enName
        : line.specificationId?.name
    const label = labelName ?? ''
    let commponents = line.specificationId.components || []
    let { apportionForm } = line
    let item = {
      apportionMoney: apportionForm.apportionMoney || '0',
      apportionPercent: apportionForm.apportionPercent
    }
    let titles = [],
      fieldValue
    commponents.map(item => {
      fieldValue = apportionForm[item.field]
      // 编辑状态下的业务对象数据
      if (fieldValue && (fieldValue.dataLink || fieldValue.entityId)) {
        if (fieldValue.dataLink) { // 编辑状态下的数据
          const entityId = get(fieldValue, `dataLink.entity.id`)
          const entityName1 = get(fieldValue, `dataLink.E_${entityId}_name`, '')
          const entityCode1 = get(fieldValue, `dataLink.E_${entityId}_code`, '')
          fieldValue.name = entityCode1 ? `${entityName1}(${entityCode1})` : entityName1
        } else if (fieldValue.entityId) { // 回显时的数据
          const entityName2 = get(fieldValue, `form.E_${fieldValue.entityId}_name`, '')
          const entityCode2 = get(fieldValue, `form.E_${fieldValue.entityId}_code`, '')
          fieldValue.name = entityCode2 ? `${entityName2}(${entityCode2})` : entityName2
        }
      }
      if (fieldValue && fieldValue.name) {
        let map = {}
        if (
          baseDataPropertiesMap[item.field] &&
          get(baseDataPropertiesMap[item.field], 'dataType.entity') === 'organization.Department'
        ) {
          map = i18n.currentLocale === 'zh-CN' ? departFullPathMap : departFullEnPathMap
          // @i18n-ignore
        } else if (
          baseDataPropertiesMap[item.field] &&
          get(baseDataPropertiesMap[item.field], 'dataType.type') === 'ref'
        ) {
          map = projectFullPahtMap
          fieldIds.push(fieldValue.id)
        }

        titles.push({
          name: isUserField(fieldValue) ? getStaffName(fieldValue) : getDisplayName(fieldValue),
          code: fieldValue.code,
          fullPath: map[fieldValue.id],
          fieldId: fieldValue.id,
          label: item.label
        })
      }
    })
    item.apportionLabel = label
    item.titles = titles
    item.apportionId = line.apportionForm.apportionId
    data.push(item)
  })
  return { data, fieldIds }
}


/**
 * @description 分摊明细自定义字段拆分为 {key,val}
 * @param data
 * @returns
 */
 export const formatApportionRemarkData = (data) => {
   const formatData = []
   const components = data?.specificationId?.components?.filter(item => item.field.startsWith('u_'))
   const apportionForm = data?.apportionForm
   components.forEach(item => {
     const { type, withTime, dateTimeType, field, label } = item
     let value = apportionForm[field]
     if (value) {
      if (type === 'text') {
        formatData.push({ label, value })
      } else if (type === 'date') {
        const format = formatDateTime(withTime, dateTimeType)
        value = typeof value === 'number' ? moment(value).format(format) : value
        formatData.push({ label, value })
      }
     }
   })
  return formatData
}
