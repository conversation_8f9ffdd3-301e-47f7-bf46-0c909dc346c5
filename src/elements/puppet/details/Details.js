/**************************************************
 * Created by nany<PERSON><PERSON>feng on 14/07/2017 15:57.
 **************************************************/
import React, { PureComponent } from 'react'
import styles from './Details.module.less'
import DetailsHeader from './DetailsHeader'
import classnames from 'classnames'
import DetailsWrapper from './DetailsWrapper'
import { DETAIL_SHOWTYPE } from '@ekuaibao/lib/lib/enums'
import { fnGetVisibleIds } from '../../../lib/fee-util'
import { app as api } from '@ekuaibao/whispered'
import { EnhanceConnect } from '@ekuaibao/store'
import { related } from '../../feeDetailViewList/Related'
import { uniq as _uniq, uniqBy as _uniqBy, throttle, cloneDeep as _cloneDeep, isEqual, get } from 'lodash'
import { formatFeeTypeList, sortTypeList, sortType, sortTypeMap, formateSortTypeMenu } from './FormatDateUtils'
import { Collapse } from 'antd'
import { isHongShanTestingEnterprise } from '@ekuaibao/lib/lib/help'
import { Fetch } from '@ekuaibao/fetch'
import EKBIcon from '../../ekbIcon'
import { filterFieldsForSort } from './DetailHelper'
import { isObject, isString } from '@ekuaibao/helpers'
import { fnGetInvoiceManage } from '../../../elements/invoice-form/utils/config'
import { detailsTemplateAddReceivingAmount } from '../../../plugins/bills/util/billUtils'
const { Panel } = Collapse

const SHOW_TYPE_MAP = {
  LIST_P: 'LIST*',
  TABLE_P: 'TABLE'
}
const noop = () => { }
const isWx = window.__PLANTFORM__ === 'WEIXIN'
const me = api.getState()['@common'].userinfo.staff
const applyDetail = {
  title: i18n.get('关联明细'),
  list: [
    {
      id: 'applyDetail',
      title: i18n.get('关联明细'),
      describe: i18n.get('通过导入申请明细生成报销明细'),
      dataLinkEntity: null,
      filterId: null
    }
  ]
}
let billInfoContainerDom

@EnhanceConnect(state => ({
  feetypeTableEdit: state['@common'].feetypeTableEdit,
  multiplePayeesMode: state['@bills'].multiplePayeesMode,
  remunerationConfig: state['@remuneration'].remunerationConfig,
  globalFields: state['@common'].globalFields.data,
  feeChangeInfo: state['@bills'].feeChangeInfo,
  userInfo: state['@common'].userinfo.staff,
  userInfoData: state['@common'].userinfo.data,
  autoExpenseWithBillStriction: state['@common'].powers.autoExpenseWithBillStriction,
}))
export default class Details extends PureComponent {
  constructor(props) {
    super(props)
    let { field, selectedData, visibleFeeTypes, isRecordExpends, allFeeTypes, isEdit, dataSource, userInfo } = props
    const visibleFeeTypeList = isRecordExpends ? allFeeTypes : visibleFeeTypes
    selectedData = this.getSelectAllData(selectedData, visibleFeeTypeList)
    const isTestingEnterprise = isHongShanTestingEnterprise(Fetch?.ekbCorpId)
    this.showTypeByRoleValue = null
    if (field?.showTypeByRole && isTestingEnterprise) {
      const userRoles = get(userInfo, 'roles.values', [])
      const userRoleIds = userRoles.map(el => el.roleDefId)
      let showTypeMethod
      if (Array.isArray(field.showTypeByRole)) {
        showTypeMethod = field.showTypeByRole.find(
          el => el?.roleIds?.filter(roleId => userRoleIds.includes(roleId)).length > 0
        )
      }
      if (showTypeMethod) this.showTypeByRoleValue = showTypeMethod.showTypeNew
    }
    // 配置模板使用新的配置值：showTypeNew 来替换 showType
    let showTypeNewVal = field?.showType
    if ((field?.showTypeNew && Array.isArray(field?.showTypeNew)) || this.showTypeByRoleValue) {
      let showTypeNew = this.showTypeByRoleValue ? _cloneDeep(this.showTypeByRoleValue) : _cloneDeep(field.showTypeNew)
      let flag = false
      if (!!~showTypeNew.indexOf('STAFF')) {
        // 如果配置中包含人员分组
        const showTypeP = showTypeNew[0]
        if (SHOW_TYPE_MAP[showTypeP]) {
          showTypeNewVal = `${SHOW_TYPE_MAP[showTypeP]}-FIELDGROUP-${showTypeNew.pop()}-E`
          flag = true
        }
      }
      if (!flag) {
        // 如果配置了新的展示方式，但是没有选择人员分组，则直接取数组最后一个值作为选定制
        showTypeNewVal = showTypeNew.pop()
      }
    }
    const showTypeValue = this.showTypeByRoleValue
      ? showTypeNewVal
      : this.getViewShowType(isTestingEnterprise) || showTypeNewVal || DETAIL_SHOWTYPE.LIST
    const sortTypeList = this.getSortTypeList(dataSource)
    try {
      props.tableVm.showType = showTypeValue
      props.bus.$showType = showTypeValue
    } catch (error) { }
    if (showTypeValue === 'TABLE_NO_GROUP') {
      // 费用类型切换展示方式
      api?.logger?.info(`费用类型默认表格编辑`, {
        showType: showTypeValue
      })
    }
    this.state = {
      isTestingEnterprise,
      showType: showTypeValue,
      fieldsGroupValue: [],
      selectedData: selectedData,
      corporationList: [],
      importList: [],
      isEnableAffix: isEdit,
      specificationComponents:
        props.specificationComponents || (props.specificationId && props.specificationId.components) || [],
      isInModal: false,
      selectedSortMenu: sortTypeList[0],
      sortDataSource: dataSource,
      foldedKey: null,
      showDetailList: true,
      isFullscreen: false,
      sortTypeList: sortTypeList,
      loadingStatus: false,
      rightInfoExpand:true,
    }
  }
  componentDidMount() {
    try {
      this.props.tableVm.showType = this.state.showType
      this.props.bus.$showType = this.state.showType
    } catch (error) { }
    this.updateImportList(this.props)
    const { template = [], multiplePayeesMode, bus } = this.props

    if (multiplePayeesMode && template.length > 0) {
      //readonly datasource 中没有了模板数据,从template里找
      const flattenTemplate = this.flat(template)
      const payeeId = flattenTemplate.find(item => item.name === 'payeeId')
      this.setState({ specificationComponents: [payeeId] })
    }
    bus && bus.on('details:table:list:width:change', this.tablleListChangeWidth)
    bus && bus.on('expenseLink:change', this.formatImportList)
    bus && bus.on('import:expenseLink:loading', this.setLoadStatus)
    api.watch('feeSortDataSource:get', this.getFeeSortDataSource)
    this.initDetailsHeaderAffix()
    this.initFieldsGroup()
  }

  tablleListChangeWidth = (isExpand) =>{
     this.setState({
       rightInfoExpand:isExpand
     })
  }

  getFeeSortDataSource = () => {
    return this.state.sortDataSource
  }

  getSortTypeList = (dataSource = []) => {
    let dateFieldsList = []
    dataSource.forEach(item => {
      const fieldsList = get(item, 'specificationId.components', [])
      const dateList = fieldsList.filter(value => value.type === 'date')
      dateFieldsList = dateFieldsList.concat(dateList)
    })
    dateFieldsList = _uniqBy(dateFieldsList, 'field')
    const dateSortTypeList = dateFieldsList.map(item => {
      return {
        type: item.field,
        label: item.label,
        children: formateSortTypeMenu(sortType.DATE, item.field, item.label)
      }
    })
    return sortTypeList.concat(dateSortTypeList)
  }
  initDetailsHeaderAffix = () => {
    billInfoContainerDom = document.getElementById('bill-info-editable-container')
    const modifyBillWrapper = document.querySelector('#modify-bill-wrapper')
    const { inModal } = this.props
    if (modifyBillWrapper || inModal) {
      // 是否再弹窗中
      this.setState({ isInModal: true })
    }

    if (billInfoContainerDom) {
      billInfoContainerDom.addEventListener('scroll', this.updateAffixDetailHeader)
    }
  }

  updateAffixDetailHeader = throttle(() => {
    const { isEnableAffix } = this.state
    const billInfoContainerDom = document.getElementById('bill-info-editable-container')
    const detailsContainerDom = document.getElementById('details-container')
    const detailsHeaderContainerDom = document.getElementById('details-header-container')
    if (detailsContainerDom && detailsHeaderContainerDom && billInfoContainerDom?.scrollTop) {
      let viewBottom =
        detailsContainerDom.offsetHeight + detailsContainerDom.offsetTop - detailsHeaderContainerDom.offsetHeight
      if (isEnableAffix && billInfoContainerDom.scrollTop > viewBottom) {
        // 滚动到可见区底部隐藏
        this.setState({ isEnableAffix: false })
      } else if (!isEnableAffix && billInfoContainerDom.scrollTop < viewBottom) {
        this.setState({ isEnableAffix: true })
      }
    }
  }, 200)

  flat = template => {
    let result = []
    for (const item of template) {
      item instanceof Array ? (result = result.concat(this.flat(item))) : result.push(item)
    }
    return result
  }
  componentWillUnmount() {
    const { bus } = this.props
    bus && bus.un('expenseLink:change', this.formatImportList)
    bus && bus.un('details:table:list:width:change', this.tablleListChangeWidth)
    api.un('feeSortDataSource:get', this.getFeeSortDataSource)
    bus && bus.un('import:expenseLink:loading', this.setLoadStatus)
    billInfoContainerDom?.removeEventListener('scroll', this.updateAffixDetailHeader)
  }

  setLoadStatus = (loading) => {
    this.setState({
      loadingStatus: loading
    })
  }

  getSpecificationType = () => {
    const { specificationId } = this.props
    const specId = typeof specificationId === 'object' ? specificationId.id : specificationId
    if (specId && typeof specId === 'string') {
      const specIdArr = specId.split(':')
      return specIdArr[0]
    }
    return undefined
  }

  formatImportList = () => {
    setTimeout(this.fnFormatImportList, 0)
  }

  fnFormatImportList = () => {
    const importList = this.state.importList.slice(0)
    const { allowAdd } = related.specificationConfig
    const { dataFromOrder } = this.props
    if (allowAdd && dataFromOrder?.showImportInPermit) {
      return this.setState({ importList: [applyDetail] })
    }
    const applyDetailIdx = importList.findIndex(v => v.id === 'applyDetail')
    if (allowAdd && applyDetailIdx === -1) {
      importList.push(applyDetail)
    }
    if (!allowAdd && applyDetailIdx > -1) {
      importList.splice(applyDetailIdx, 1)
    }
    this.setState({ importList })
  }

  updateImportList = props => {
    let { isEdit, specificationId, type, billSpecification } = props
    const { allowAdd } = related.specificationConfig
    const { dataFromOrder } = this.props
    if (allowAdd && dataFromOrder?.showImportInPermit) {
      return this.setState({ importList: [applyDetail] })
    }
    let id = typeof specificationId === 'object' ? specificationId.id : specificationId
    if (isEdit && id) {
      api.invokeService('@custom-specification:get:feetypeImportRule:byId', { id, formType: type, appId: billSpecification?.appId }).then(result => {
        let { items } = result
        if (allowAdd) {
          items.push(applyDetail)
        }
        this.setState({ importList: items })
      })
    }
  }
  componentWillReceiveProps(nextProps) {
    if (this.props.selectedData !== nextProps.selectedData) {
      const { isRecordExpends, allFeeTypes, visibleFeeTypes } = nextProps
      const visibleFeeTypeList = isRecordExpends ? allFeeTypes : visibleFeeTypes
      const selectedData = this.getSelectAllData(nextProps.selectedData, visibleFeeTypeList)
      this.setState({ selectedData })
    }
    if (nextProps.specificationId !== this.props.specificationId) {
      this.updateImportList(nextProps)
    }
    if (!isEqual(this.props.dataSource, nextProps.dataSource)) {
      // TM 处理
      const sortTypeList = this.getSortTypeList(nextProps.dataSource)
      this.setState({ sortDataSource: nextProps.dataSource })
      this.setState({ selectedSortMenu: sortTypeList[0], sortTypeList })
    }
  }

  getSelectAllData = (dataSource = [], visibleFeeTypes = []) => {
    const result = []
    const visibleIds = fnGetVisibleIds(visibleFeeTypes)
    dataSource.forEach(item => {
      if (!!~visibleIds.indexOf(item.feeTypeId.id)) {
        result.push(item)
      }
    })
    return result
  }

  handleChangeShowDetailList = () => {
    const { showDetailList } = this.state
    this.setState({ showDetailList: !showDetailList })
  }

  handleChangeView = ({ key }) => {
    this.props.tableVm.showType = key
    let message = `从[${this.state.showType}]切换到[${key}]`
    if (key === 'TABLE_NO_GROUP') {
      this.props?.tableVm?.TM?.handleDataSource(this?.props?.dataSource || [], true)
      message = `从其他类型[${this.state.showType}]切换到[表格编辑]`
    }
    if (this.state.showType === 'TABLE_NO_GROUP') {
      this.props.tableVm.editingKey = ''
      this.props.tableVm.TM.billBus.$isTableEdit = false
      message = `从[表格编辑]切换到其他类型[${key}]`
    }
    // 费用类型切换展示方式
    api?.logger?.info(`费用类型切换`, {
      prevType: this.state.showType,
      nextType: key,
      message
    })
    this.props.bus.$showType = key
    if(this.props.bus.has('active:risk:go:check')){
      this.props.bus.emit('active:risk:go:check',key === 'LIST')
    }
    this.setViewShowType(key)
    this.props.tableVm.loading = false
    this.setState({ showType: key }, () => this.updateAffixDetailHeader())
  }

  getSelectedSortMenu = key => {
    let selectedSortMenu = {}
    this.state.sortTypeList?.forEach(item => {
      if (item.type === key) {
        selectedSortMenu = item
      } else if (item.children) {
        const data = item.children.find(cItem => cItem.type === key)
        if (data) selectedSortMenu = data
      }
    })
    return selectedSortMenu
  }

  handleChangeSort = ({ key }) => {
    const { isEdit } = this.props
    const selectedSortMenu = this.getSelectedSortMenu(key)
    const sortDataSource = isEdit ? _cloneDeep(this.state.sortDataSource) : this.state.sortDataSource
    if (key === sortType.INPUT) {
      this.setState({ sortDataSource: this.props.dataSource })
      this.refreshTable(this.props.dataSource)
    } else if (key === sortTypeMap.AMOUNT.ASC) {
      const sortData = sortDataSource.sort((p, n) => {
        return Number(p.feeTypeForm?.amount?.standard || 0) < Number(n.feeTypeForm?.amount?.standard || 0) ? -1 : 1
      })
      this.refreshTable(sortData)
      this.setState({ sortDataSource: sortData })
    } else if (key === sortTypeMap.AMOUNT.DESC) {
      const sortData = sortDataSource.sort((p, n) => {
        return Number(p.feeTypeForm?.amount?.standard || 0) < Number(n.feeTypeForm?.amount?.standard || 0) ? 1 : -1
      })
      this.refreshTable(sortData)
      this.setState({ sortDataSource: sortData })
    } else {
      const sortData = this.fnChangeSort(sortDataSource, key, 'feeTypeForm')
      this.refreshTable(sortData)
      this.setState({ sortDataSource: sortData })
    }
    this.setState({ selectedSortMenu })
  }

  refreshTable = details => {
    if (this.props.tableVm?.showType === 'TABLE_NO_GROUP') {
      this.props.tableVm?.TM?.handleDataSource(details, true)
    }
  }

  fnChangeSort = (sortDataSource = [], key = '', path = '') => {
    const sort_type = key.includes(sortType.ASC) ? sortType.ASC : sortType.DESC
    const field = key.slice(sort_type.length + 1) || ''
    const field_path = `${path}.${field}`
    const fieldSortData = sortDataSource.filter(item => !!get(item, field_path, 0))
    const noFieldSortData = sortDataSource.filter(item => !get(item, field_path, 0))
    let sortData = []
    if (sort_type === sortType.ASC) {
      sortData = fieldSortData.sort((p, n) => {
        return Number(get(p, field_path, 0)) - Number(get(n, field_path, 0))
      })
    } else {
      sortData = fieldSortData.sort((p, n) => {
        return Number(get(n, field_path, 0)) - Number(get(p, field_path, 0))
      })
    }
    return sortData.concat(noFieldSortData)
  }

  getGlobalFieldsId = () => {
    const globalFieldsData = api.getState('@common.globalFields.data') || []
    const singleStaffFields = filterFieldsForSort(globalFieldsData)
    return singleStaffFields.map(item => item.name)
  }

  initFieldsGroup = () => {
    this.handleChangeFieldsGroup()
  }

  handleChangeFieldsGroup = (val) => {
    const value = val || this.getGlobalFieldsId()
    const singleStaffFields = filterFieldsForSort(this.props.globalFields)
    // 如果个人没有配置，查看模板配置信息
    if (!value || !value.length) {
      let { showType, showTypeNew } = this.props.field || {}
      if (showTypeNew && Array.isArray(showTypeNew)) {
        showTypeNew = _cloneDeep(showTypeNew)
        // 取数组最后一个值，且返回一个数组，作为默认的维度展示字段
        value = showTypeNew.splice(-1, 1)
      } else {
        value = [showType]
      }
    }
    const resultStaffFields = value
      .map(name => {
        const matchSingleStaff = singleStaffFields.filter(item => item.name === name)
        return matchSingleStaff[0] ? matchSingleStaff[0] : undefined
      })
      .filter(item => item)
    this.setState(
      {
        fieldsGroupValue: resultStaffFields
      },
    )
  }
  setViewShowType = obj => {
    const key = me?.id + '-' + this.getSpecificationType() + '-' + 'feeDetailShowType'
    if (isWx) {
      session.set(key, JSON.stringify(obj))
    } else {
      localStorage.setItem(key, JSON.stringify(obj))
    }
  }
  getViewShowType(isTestingEnterprise) {
    if (isTestingEnterprise) return undefined
    const specificationType = this.getSpecificationType()
    const key = me?.id + '-' + specificationType + '-' + 'feeDetailShowType'
    const ShowType = isWx ? session.get(key) : localStorage.getItem(key)
    try {
      return specificationType && ShowType ? JSON.parse(ShowType) : undefined
    } catch (e) {
      return undefined
    }
  }
  onCheckedAllClick = e => {
    const { isEdit, dataSource, handleChange = noop, visibleFeeTypes, billSpecification } = this.props
    const allowDeleteFeeDetail = billSpecification?.configs?.find(v => v.ability === 'apply')?.autoAssociateOrderConfig
      ?.allowDeleteFeeDetail
    const checked = e.target.checked
    let selectedData = checked ? (isEdit ? dataSource : this.getSelectAllData(dataSource, visibleFeeTypes)) : []
    if (allowDeleteFeeDetail === false) {
      selectedData = selectedData.filter(v => !v.feeTypeForm?.systemGenerationDetail)
    }
    this.setState({ selectedData })
    this?.props?.tableVm?.TM?.updateSelectData(selectedData.map(item => item.idx))
    handleChange(selectedData)
  }

  handleChange = selectedData => {
    const { handleChange = noop } = this.props
    this.setState({ selectedData })
    handleChange(selectedData)
  }

  editable = () => {
    const { isEdit, feeDetailUneditable } = this.props
    return isEdit && !feeDetailUneditable
  }

  handleDataSourceByGroupFields = (dataSource = []) => {
    const { showType = '', fieldsGroupValue = [], selectedSortMenu } = this.state
    if (fieldsGroupValue && fieldsGroupValue.length) {
      const collapseDefaultKeys = new Set()
      const allGroupKey = []
      const regExp = /\-FIELDGROUP\-(.*)\-E/g
      const complieResult = regExp.exec(showType)
      if (complieResult && complieResult[1]) {
        const showTypeField = complieResult[1]
        if (showTypeField) {
          // 获取所有的分组字段对应的值
          let dataSourceFiledValueArr = _uniq(
            dataSource.map(item => {
              const fieldValue = item.feeTypeForm[showTypeField]
              if (isObject(fieldValue)) {
                return fieldValue?.name
              }
              if (isString(fieldValue)) {
                return fieldValue?.trim()
              }
              return fieldValue
            })
          )
          const showTypeFieldLabel = fieldsGroupValue.find(fieldsGroupValueItem => fieldsGroupValueItem.name === showTypeField).label || ''
          dataSourceFiledValueArr.push('others')
          let newDataSource = []
          dataSourceFiledValueArr.forEach(dataSourceFiledValueItem => {
            if (dataSourceFiledValueItem !== 'others') {
              const currentDataSource =
                dataSource.filter(dataSourceItem => {
                  const fieldValue = dataSourceItem.feeTypeForm[showTypeField]
                  if (fieldValue) {
                    if (isObject(fieldValue)) {
                      return fieldValue?.name === dataSourceFiledValueItem
                    }
                    if (isString(fieldValue) && isString(dataSourceFiledValueItem)) {
                      return fieldValue?.trim() === dataSourceFiledValueItem?.trim()
                    }
                    return fieldValue === dataSourceFiledValueItem
                  }
                  return false
                }) || []
              if (currentDataSource && currentDataSource.length) {
                const fieldGroupTitle = `${showTypeFieldLabel}: ${dataSourceFiledValueItem}`
                const key = currentDataSource[0].feeTypeForm[showTypeField].id
                allGroupKey.push(key)
                collapseDefaultKeys.add(key)
                newDataSource.push({
                  key,
                  fieldGroupTitle,
                  dataSource: currentDataSource
                })
              }
            } else {
              const isEmptyValue = (value) =>  [null, undefined, ''].includes(value)
              const others =
                dataSource.filter(dataSourceItem => {
                  return isEmptyValue(dataSourceItem.feeTypeForm[showTypeField])
                }) || []
              if (others && others.length) {
                const othersGroupKey = 'others'
                allGroupKey.push(othersGroupKey)
                newDataSource.push({
                  key: othersGroupKey,
                  fieldGroupTitle: '其他',
                  dataSource: others
                })
              }
            }
          })
          console.log('dataSourceFiled', {
            showType,
            showTypeField,
            showTypeFieldLabel,
            dataSourceFiledValueArr,
            newDataSource
          })
          let sortDataSource = []
          if (selectedSortMenu.type === sortType.INPUT) {
            sortDataSource = newDataSource
          } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.ASC) {
            sortDataSource = newDataSource.sort((p, n) => {
              return Number(
                p.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0
              ) <
                Number(
                  n.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) ||
                  0
                )
                ? -1
                : 1
            })
          } else if (selectedSortMenu.type === sortTypeMap.AMOUNT.DESC) {
            sortDataSource = newDataSource.sort((p, n) => {
              return Number(
                p.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) || 0
              ) <
                Number(
                  n.dataSource.map(line => line.feeTypeForm.amount.standard).reduce((a, b) => Number(a) + Number(b)) ||
                  0
                )
                ? 1
                : -1
            })
          } else {
            sortDataSource = this.fnChangeSort(newDataSource, selectedSortMenu.type, 'dataSource[0].feeTypeForm')
          }
          if (this.showTypeByRoleValue && collapseDefaultKeys.size === 0) collapseDefaultKeys.add('others')
          return {
            showFieldsGroup: true,
            dataSource: sortDataSource,
            collapseDefaultKeys,
            allGroupKey
          }
        }
      }
    }
    return {
      showFieldsGroup: false,
      dataSource
    }
  }

  handleCollapse = (currentGroupKeys, allGroupKey) => {
    const foldedKey = allGroupKey.filter(el => !currentGroupKeys.includes(el))
    this.setState({ foldedKey })
  }

  renderDetailsList = () => {
    const {
      submitterId = {},
      onLineClick = noop,
      onAddDetailClick = noop,
      onDelDetailClick = noop,
      selectAble = false,
      type,
      tableVm,
      tagDataSource,
      visibleFeeTypes,
      baseDataProperties = [],
      external,
      isForbid,
      riskInfo,
      fnUpdateBill,
      field,
      isInHistory,
      isRecordExpends,
      isModify,
      showAllFeeType,
      apportionVisibleList,
      billState,
      showPayPlan,
      fnShareAction,
      isCopyDetail,
      remunerationConfig,
      currentNode,
      specificationId,
      inRepaymentModal,
      billSpecification,
      configs,
      feeChangeInfo,
      autoExpenseWithBillStriction,
      bus,
      allowSelectionReceivingCurrency
    } = this.props
    const {
      selectedData,
      showType,
      corporationList,
      specificationComponents,
      sortDataSource: dataSourceList,
      selectedSortMenu,
      isFullscreen,
      foldedKey
    } = this.state
    const isRemuneration =
      remunerationConfig && remunerationConfig.specificationId == (specificationId?.id || specificationId)
    const dataSource = formatFeeTypeList(dataSourceList, feeChangeInfo)

    const {
      showFieldsGroup = false,
      dataSource: newDataSource,
      collapseDefaultKeys,
      allGroupKey
    } = this.handleDataSourceByGroupFields(dataSource)
    const renderDetailsComponent = dataSourceItem => (
      <DetailsWrapper
        specificationComponents={specificationComponents}
        isCopyDetail={isCopyDetail}
        field={field}
        dataSource={dataSourceItem}
        submitterId={submitterId}
        visibleFeeTypes={visibleFeeTypes}
        selectedData={selectedData}
        selectAble={selectAble}
        billState={billState}
        isEdit={this.editable()}
        isRecordExpends={isRecordExpends}
        isModify={isModify}
        type={type}
        tableVm={tableVm}
        tagDataSource={tagDataSource}
        currentNode={currentNode}
        external={external}
        riskInfo={riskInfo}
        showType={showType}
        isInHistory={isInHistory}
        corporationList={corporationList}
        baseDataProperties={baseDataProperties}
        handleChange={this.handleChange}
        onLineClick={onLineClick}
        onDelDetailClick={onDelDetailClick}
        onAddDetailClick={onAddDetailClick}
        fnUpdateBill={fnUpdateBill}
        isForbid={isForbid}
        showPayPlan={showPayPlan}
        showAllFeeType={showAllFeeType}
        apportionVisibleList={apportionVisibleList}
        fnShareAction={fnShareAction}
        isRemuneration={isRemuneration}
        billSpecification={billSpecification}
        selectedSortMenu={selectedSortMenu}
        bus={bus}
        onSortGroupsChange={this.fnChangeSort}
        isFullscreen={isFullscreen}
        autoExpenseWithBillStriction={autoExpenseWithBillStriction}
        allowSelectionReceivingCurrency={allowSelectionReceivingCurrency}
      />
    )

    if (this.showTypeByRoleValue && showFieldsGroup) {
      if (!newDataSource?.length) return null
      const activeKey = foldedKey
        ? allGroupKey.filter(groupKey => !foldedKey.includes(groupKey))
        : [...collapseDefaultKeys]
      return (
        <Collapse
          className={styles['fields-group-details-collapse-wrapper']}
          onChange={currentGroupKeys => this.handleCollapse(currentGroupKeys, allGroupKey)}
          activeKey={activeKey}
        >
          {newDataSource.map((newDataSourceItem, index) => {
            const { fieldGroupTitle, dataSource: dataSourceItem = [], key } = newDataSourceItem
            return (
              <Panel key={key} header={fieldGroupTitle}>
                {renderDetailsComponent(dataSourceItem)}
              </Panel>
            )
          })}
        </Collapse>
      )
    }
    if (showFieldsGroup) {
      return (
        <div className={styles['fields-group-details-wrapper-container']}>
          {newDataSource.map(newDataSourceItem => {
            const { fieldGroupTitle, dataSource: dataSourceItem = [] } = newDataSourceItem
            return (
              <div className={styles['details-wrapper-container']}>
                <div className={styles['field-group-title']}>{fieldGroupTitle}</div>
                <div>{renderDetailsComponent(dataSourceItem)}</div>
              </div>
            )
          })}
        </div>
      )
    }
    return (
      <DetailsWrapper
        specificationComponents={specificationComponents}
        isCopyDetail={isCopyDetail}
        field={field}
        dataSource={dataSource}
        submitterId={submitterId}
        visibleFeeTypes={visibleFeeTypes}
        selectedData={selectedData}
        selectAble={selectAble}
        billState={billState}
        currentNode={currentNode}
        isEdit={this.editable()}
        isRecordExpends={isRecordExpends}
        isModify={isModify}
        type={type}
        tableVm={tableVm}
        tagDataSource={tagDataSource}
        external={external}
        riskInfo={riskInfo}
        showType={showType}
        isInHistory={isInHistory}
        corporationList={corporationList}
        baseDataProperties={baseDataProperties}
        handleChange={this.handleChange}
        onLineClick={onLineClick}
        onDelDetailClick={onDelDetailClick}
        onAddDetailClick={onAddDetailClick}
        fnUpdateBill={fnUpdateBill}
        isForbid={isForbid}
        showPayPlan={showPayPlan}
        showAllFeeType={showAllFeeType}
        apportionVisibleList={apportionVisibleList}
        fnShareAction={fnShareAction}
        isRemuneration={isRemuneration}
        billSpecification={billSpecification}
        inRepaymentModal={inRepaymentModal}
        configs={configs}
        selectedSortMenu={selectedSortMenu}
        bus={bus}
        onSortGroupsChange={this.fnChangeSort}
        isFullscreen={isFullscreen}
        autoExpenseWithBillStriction={autoExpenseWithBillStriction}
        allowSelectionReceivingCurrency={allowSelectionReceivingCurrency}
      />
    )
  }
  onFullscreen = () => {
    this.setState({ isFullscreen: !this.state.isFullscreen })
  }
  render() {
    const {
      dataSource = [],
      isModify,
      onAddDetailClick = noop,
      onCopyDetailClick = noop,
      handleCheckFeeType = noop,
      onImportInvoiceClick = noop,
      onImportInvoiceOCRClick = noop,
      onImportOCRMedicalClick = noop,
      onImportInputInvoiceClick = noop,
      onRecordExpendsClick = noop,
      onQuickExpendsClick = noop,
      onImportCSCClick = noop,
      onBatchApportionClick = noop,
      onBatchRemoveDetailClick = noop,
      onImportExcelClick = noop,
      onThirdPartyImportClick = noop,
      onSelectDataLink = noop,
      onImportApplyDetail = noop,
      onImportAliPayInvoiceClick = noop,
      onRemoveToRecordExpendsClick = noop,
      onImportAifaPiaoInvoiceClick = noop,
      onImportOverseasInvoiceClick = noop,
      thirdPartyList = [],
      type,
      configs,
      importAble,
      field,
      hasHeader = true,
      unvisibleCount,
      billType,
      remunerationConfig,
      billState,
      specificationId,
      hideSortBtn,
      isEdit,
      bus,
      billSpecification,
      feetypeTableEdit,
      flowId,
      tableVm,
      tagDataSource,
      submitterId,
      userInfoData: { permissions },
      riskData,
      autoExpenseWithBillStriction,
      feeChangeInfo,
      allowSelectionReceivingCurrency
    } = this.props
    const editAble = this.editable()
    const {
      selectedData,
      showType,
      importList,
      fieldsGroupValue,
      isEnableAffix,
      isInModal,
      selectedSortMenu,
      showDetailList,
      isFullscreen,
      sortTypeList,
      sortDataSource: dataSourceList,
      loadingStatus,
      rightInfoExpand
    } = this.state
    const isPermitForm = tagDataSource?.openFrom === 'permit-form'
    const cls = classnames(styles.details, {
      [styles['details-read']]: !editAble,
      'dis-none': !dataSource?.length && !isEdit,
      [styles['details-fullscreen-wrapper']]: isFullscreen,
      [styles['details-permit-form']]: isPermitForm
    })
    const isRemuneration =
      remunerationConfig && remunerationConfig.specificationId == (specificationId?.id || specificationId)
    let style = {}
    // const newBillInfoViewWidth = document?.getElementById('NewBillInfoView')?.clientWidth
    // if (newBillInfoViewWidth && !isFullscreen) {
    //   const padding = 75
    //   const billInfoWidth = rightInfoExpand ? 280 :0
    //   style.maxWidth = newBillInfoViewWidth - padding - billInfoWidth + 'px'
    // }
    const isInvoiceManagePermissions = !!fnGetInvoiceManage(permissions)
    const formatDataSource = formatFeeTypeList(dataSourceList, feeChangeInfo)
    const dataSourceAddReceivingAmount = detailsTemplateAddReceivingAmount(dataSource)
    const { dataSource: newDataSource } = this.handleDataSourceByGroupFields(formatDataSource)

    return (
      <div style={style} className={cls} id={`${editAble && !isRemuneration ? 'details-container' : 'details-container2'}`}>
        {hasHeader && (
          <DetailsHeader
            loadingStatus={loadingStatus}
            feetypeTableEdit={feetypeTableEdit || false}
            billType={billType}
            importAble={importAble}
            importList={importList}
            configs={configs}
            isEdit={editAble && !isRemuneration}
            type={type}
            dataSourceOld={dataSourceAddReceivingAmount} //有的布局里面newDataSource的格式被分组了，导致计算有问题
            tableVm={tableVm}
            tagDataSource={tagDataSource}
            field={field}
            isFullscreen={isFullscreen}
            isEnableAffix={isEnableAffix && !isFullscreen}
            selectedData={selectedData}
            showType={showType}
            thirdPartyList={thirdPartyList}
            isModify={isModify}
            fieldsGroupValue={fieldsGroupValue}
            unvisibleCount={unvisibleCount}
            onAddDetailClick={onAddDetailClick}
            onCopyDetailClick={onCopyDetailClick}
            handleCheckFeeType={handleCheckFeeType}
            onImportInvoiceClick={onImportInvoiceClick}
            onImportInvoiceOCRClick={onImportInvoiceOCRClick}
            onImportOCRMedicalClick={onImportOCRMedicalClick}
            onImportInputInvoiceClick={onImportInputInvoiceClick}
            onImportAliPayInvoiceClick={onImportAliPayInvoiceClick}
            onImportAifaPiaoInvoiceClick={onImportAifaPiaoInvoiceClick}
            onRecordExpendsClick={onRecordExpendsClick}
            onQuickExpendsClick={onQuickExpendsClick}
            onImportCSCClick={onImportCSCClick}
            onBatchApportionClick={onBatchApportionClick}
            onBatchRemoveDetailClick={onBatchRemoveDetailClick}
            onThirdPartyImportClick={onThirdPartyImportClick}
            onCheckedAllClick={this.onCheckedAllClick}
            onChangeView={this.handleChangeView}
            onChangeFieldsGroup={this.handleChangeFieldsGroup}
            onImportExcelClick={onImportExcelClick}
            onSelectDataLink={onSelectDataLink}
            onImportApplyDetail={onImportApplyDetail}
            onImportOverseasInvoiceClick={onImportOverseasInvoiceClick}
            onFullscreen={this.onFullscreen}
            dataSource={newDataSource}
            onRemoveToRecordExpendsClick={onRemoveToRecordExpendsClick}
            billState={billState}
            isInModal={isInModal}
            selectedSortMenu={selectedSortMenu}
            onFilterSortMenu={this.handleChangeSort}
            hideSortBtn={hideSortBtn}
            toApportionCheck={{ billSpecification, bus }}
            handleChangeShowDetailList={this.handleChangeShowDetailList}
            showDetailList={showDetailList}
            flowId={flowId}
            submitterId={submitterId}
            isInvoiceManagePermissions={isInvoiceManagePermissions}
            riskData={riskData}
            sortTypeList={sortTypeList}
            autoExpenseWithBillStriction={autoExpenseWithBillStriction}
            allowSelectionReceivingCurrency={allowSelectionReceivingCurrency}
          />
        )}
        {showDetailList && this.renderDetailsList()}
        {!isEdit && showDetailList && (
          <div className="fold-btn" onClick={this.handleChangeShowDetailList}>
            <EKBIcon name="#EDico-icon_uni_drop-title" />
            {i18n.get('收起')}
          </div>
        )}
      </div>
    )
  }
}
