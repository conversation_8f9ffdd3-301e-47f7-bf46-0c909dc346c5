/**
 *  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 17/9/11 下午3:03.
 */
import styles from './DetailsItem.module.less'
import React from 'react'
import { Checkbox } from '@hose/eui'
import DetailsItemBase from './DetailsItemBase'
import DetailsItemApportion from '../apportions/DetailsItemApportion'
import { fnHideFields, isHiddenFieldsInclude } from "../../../components/utils/fnHideFields";
import { get, cloneDeep } from 'lodash'

export default function DetailsItem(props) {
  let {
    hiddenFields,
    dataSource,
    onLineClick,
    onChangeChecked,
    selectAble,
    selectedData,
    feeSelectAble,
    index,
    onVisibleChange,
    isRecordExpends,
    apportionVisibleList,
    isModify,
    isEditable,
    showAllFeeType,
    isCopyDetail,
  } = props
  let apportions = dataSource.feeTypeForm.apportions || []
  let isReadFirstNoBorder = dataSource.idx === 0 && !selectAble //单据只读状态第一条消费明细 不显示borderTop

  // const checked = !!~selectedData.indexOf(dataSource)
  const checked = selectedData.find(item => item.idx === dataSource.idx)
  const handleChange = e => {
    const selected = e.target.checked
    onChangeChecked([dataSource], selected)
  }
  const handleClick = () => {
    onLineClick && onLineClick(dataSource, index)
  }

  return (
    <div className={styles['details-item']} style={isRecordExpends ? { paddingLeft: 0 } : {}}>
      {selectAble && (
        <div className="checked">
          <Checkbox checked={checked} disabled={!feeSelectAble} onChange={handleChange} />
        </div>
      )}
      <div className="detail-content" onClick={handleClick}>
        <DetailsItemBase
          {...props}
          isShowBorder={true}
          isReadFirstNoBorder={isReadFirstNoBorder}
          onVisibleChange={onVisibleChange}
        />
        {apportions.length > 0 && (
          <DetailsItemApportion
            isModify={isModify}
            isCopyDetail={isCopyDetail}
            isEditable={isEditable}
            showAllFeeType={showAllFeeType}
            apportionVisibleList={apportionVisibleList}
            dataSource={apportions}
          />
        )}
      </div>
    </div>
  )
}
