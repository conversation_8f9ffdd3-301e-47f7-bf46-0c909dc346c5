interface IOEMConfig {
  hide: boolean // 是否隐藏组件
  url: string // 配置的跳转的url比如客服链接
}

interface Window {
  isNewHome: boolean
  TRACK: Function
  NOEKBSET: any

  __PLANTFORM__: string
  IS_LDAP: boolean
  $_privilegeId: string
  APPLICATION_VERSION: string

  IS_HSFK: boolean
  IS_STANDALONE: boolean
  IS_CMBC: boolean
  IS_ICBC: boolean
  IS_NOEKB: boolean
  IS_SZJL: boolean
  IS_ZJZY: boolean
  HUAWEI_LOGIN: string
  PLATFORMINFO: any
  IS_SMG: boolean
  IS_OPG: boolean
  IS_LDAP: boolean
  PREVIEW_DOMAIN: string
  AI_HOST: {
    [key:string]:string
  }
  NODE_ENV: string
  layout5MenuScrollTop: number // 记录菜单滚动位置
  inGroupApp: boolean // 判断是否在集团版中
  GROUP_URL: string // 集团版环境的地址
  APP_URL: string // 原生环境的地址
  SYNC_TEMPLATE_ORIGIN: string
  corpConfig: {
    // 平台企业特殊配置，比如隐藏logo，隐藏易快报标识，目前支持测配置如下
    BUSINESS_ICON?: IOEMConfig // 企业图标链接
    BUSINESS_LOGOUT?: IOEMConfig // 退出登陆
    CREATE_CORP?: IOEMConfig // 创建企业
    HELP_DEMAND?: IOEMConfig //  帮助中心-提个需求
    HELP_GUID?: IOEMConfig // 帮助中心-帮助手册
    HELP_LOG?: IOEMConfig // 帮助中心-更新日至
    HELP_SERVICE?: IOEMConfig // 帮助中心-客服
    BUSINESS_EKB?: IOEMConfig // '易快报'标识
    documentTitle?: string // 浏览器页签的名字
  }
  isZhongDian: boolean //判断是否是研究所环境
  isSharedApp: boolean //轻共享新环境
  getCorpStyle: () => any // 企业品牌化设置
  __HOME_PAGE_LOADED_TS?: number // 首页资源加载完开始渲染时间戳
  HSM?: any // 观测云实例
  __HAB_BaseURL?: string // 组件加载基础路径
  __HAB_CorpId?: string // 企业id
  __HAB_AcessToken?: string // token
  __HAB_StaffId?: string // 当前登录人id
  __HAB_DepartmentId?: string //当前登录人部门id
  __HAB_DataType?: string // 当前单据类型
  __HAB_EkbCode?: string // ekbCode
  __HAB_FlowId?: string // 单据ID
  __HAB_FlowCode?: string // 单据Code
  __HAB_Components__?: Map<string, any> // 组件缓存
  /**
   * 翻译插件
  */
  TranslateConfig: any
  /**
   * 天润客服插件
   */
  clinkWebchatOptions: (options: {
    accessId: string
    externalId: string
    customerFields: string
    language: string
  }) => void
  /**
   * 天润客服插件实例
   */
  ClinkChatWeb?: {
    registerUnRead: (callback: (...args: any[]) => void) => void
    getUnReadNum: () => number
    openSessionWindow: () => void
  },
  // 环境中配置的变量，nginx服务下发
  ENV_CONFIG: IEnvConfig,
  // 获取环境变量的统一方法
  getEnvConfigValue: (configKey: keyof IEnvConfig, defaultValue: string) => string

  __BIRD_CONFIG?: any
}

interface IEnvConfig {
  // 微前端宿主id
  mfeHostId: string
  // 微前端服务地址
  mfeURL: string
  // 观测云上报地址
  ddTraceURL: string
  // idp授权服务域名
  idpDomain: string
  // featBit服务域名
  featBitDomain: string
  // featBitClientKey
  featBitClientKey: string
  // unity域名
  unityDomain: string
}

declare module '*.png' {
  const value: string
  export default value
}

declare module '*.js'

declare module 'ekbc-enhance-stacker-manager' {
  const EnhanceStackerManager: any
  export { EnhanceStackerManager }
}

declare const IS_HSFK: boolean
declare const IS_STANDALONE: boolean
declare const APPLICATION_VERSION: string
declare const IS_CMBC: boolean
declare const IS_NOEKB: boolean
declare const IS_SZJL: boolean
declare const IS_ZJZY: boolean
declare const HUAWEI_LOGIN: string
declare const IS_SMG: boolean
declare const PREVIEW_DOMAIN: string
declare const i18n: any
declare const IS_LDAP: boolean
declare const SYNC_TEMPLATE_ORIGIN: string
declare const IS_ICBC: boolean
declare const IS_OPG: boolean
declare const AI_HOST: {
  [key:string]:string
}

declare interface StringAnyProps {
  [propName: string]: any
}

declare interface Staff extends StringAnyProps {
  name: string
  nameSpell: string
  order?: Record<string, number>
}
