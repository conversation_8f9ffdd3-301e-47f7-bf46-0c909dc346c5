/**************************************************
 * Created by nanyuantingfeng on 2018/9/21 18:27.
 **************************************************/
import React from 'react'
import Loading from '@ekuaibao/loading'
import { asyncComponentConfig } from '@ekuaibao/async-component'
import { get } from 'lodash'
import { app } from '@ekuaibao/whispered'
import { session } from '@ekuaibao/session-info'
import './FetchConfig'

import '../lib/cloneDeepCache'

asyncComponentConfig({
  LoadingComponent: () => (
    <Loading
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center'
      }}
      className="layout-content-right center"
      color="var(--brand-base)"
    />
  ),

  ErrorComponent: error => {
    const name = get(error, 'error.name')
    let imgUrl, desc
    if (name === 'TypeError') {
      imgUrl = require('../images/error-type.png')
      desc = i18n.get('未知问题，请联系系统管理员')
    } else {
      imgUrl = require('../images/error-system.png')
      desc = (
        <div>
          {i18n.get('当前应用版本过低，请')}
          <div
            style={{ display: 'inline', cursor: 'pointer', textDecoration: 'underline', color: 'var(--brand-base)' }}
            onClick={() => {
              window.location.reload()
            }}
          >
            {i18n.get('刷新')}
          </div>
          {i18n.get('重试-1')}
        </div>
      )
    }
    const user: any = session?.get?.('user')
    app.logger?.error?.('ErrorComponent', { error, user })
    console.error(error)
    return (
      <div style={{ width: '100%' }}>
        <div className="center" style={{ marginTop: 200, marginBottom: 24 }}>
          <img width="300px" height="300px" src={imgUrl} />
        </div>
        <div style={{ width: '100%', height: '100%', textAlign: 'center', color: 'rgba(29,43,61,0.75)', fontSize: 14 }}>
          {desc}
        </div>
      </div>
    )
  }
})

import './updateScopeVariable'
