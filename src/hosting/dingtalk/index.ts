import { reportHomePageLoadedDuration, reportHomePagePaintDuration } from '../../logger/homePageLoad'

require('../polyfill')

if (process.env.NODE_ENV === 'production' && (
  /dd2\.ekuaibao\.com/.test(window.location.hostname) ||
  /ekuaibao2297\.eapps\.dingtalkcloud\.com/.test(window.location.hostname)
)) {
  require('./dingdingProbe')
}

try {
  if (process.env.NODE_ENV === 'production' && location.hostname === 'dd2.hosecloud.com') {
    window.__BIRD_CONFIG = window.__BIRD_CONFIG || {}
    window.__BIRD_CONFIG.tags = {
      isvAppId: '2297' // isv应用id
    }
  }
} catch(e) {}

if (!Object.values) {
  Object.values = function(obj) {
    if (obj !== Object(obj))
        throw new TypeError('Object.values called on a non-object');
    var val=[],key;
    for (key in obj) {
        if (Object.prototype.hasOwnProperty.call(obj,key)) {
            val.push(obj[key]);
        }
    }
    return val;
  }
}

window.addEventListener('load', () => {
  reportHomePageLoadedDuration()
  reportHomePagePaintDuration()
})

window.addEventListener('DOMContentLoaded', async () => {
  await require('./bootstrap').bootstrap()
})
