interface FMPMonitorParams {
  title: string,
  [key: string]: any
}

export interface FMPMonitorOptions {
  isFMPNode: (n: Element) => boolean,
  // 自定义埋点参数
  params: FMPMonitorParams | (() => FMPMonitorParams),
  rule: (hash?: string) => boolean
}

// 菜单
const existMenu = (parentNode: Element | Document = document) => {
  return !!(
    parentNode?.querySelector('.layout5-menu-item') ||
    parentNode?.querySelector('.layout5-sub-menu-item')
  )
}
const isMenu = (node: Element) => node?.classList?.contains('layout5-menu-item') || node?.classList?.contains('layout5-sub-menu-item') || existMenu(node)

// 我的单据卡片
const existBillCard = (parentNode: Element | Document = document) => {
  return !!(
    parentNode?.querySelector('#card-my-bills div[data-testid="home-card-empty"]') ||
    parentNode?.querySelector('#card-my-bills .bill-info-title-wrapper')
  )
}
const isBillCard = (node: Element) => ['home-card-empty'].includes((node as HTMLElement)?.dataset?.testid) || node?.classList?.contains('bill-info-title-wrapper') || existBillCard()


// DX 表格通用
const existDxTableCell = (parentNode: Element | Document = document) => {
  return !!(parentNode?.querySelector('td[role="gridcell"]') || parentNode?.querySelector('.dx-datagrid-nodata'))
}

const isDxTableCell = (node: Element) => {
  return (node?.tagName === 'TD' && node?.role === 'gridcell') || // 表格模式
    node?.classList?.contains('.dx-datagrid-nodata') ||  // 表格模式-无数据
    existDxTableCell()
}


// 我的单据 - 页面
const existMyBills = (parentNode: Element | Document = document) => {
  return !!(
    parentNode?.querySelector('#ListPartDataContainer') || existDxTableCell(parentNode)
  )
}
const isMyBills = (node: Element) => {
  return node.id === 'ListPartDataContainer' || // 列表模式
    isDxTableCell(node) || // 表格模式
    existMyBills()
}



export const FMP_MONITOR_CONFIG_MAP: Record<string, FMPMonitorOptions> = {
  home: {
    isFMPNode: node => {
      return (isMenu(node) && existBillCard()) || (isBillCard(node) && existMenu())
    },
    params: () => ({
      title: '首页FMP'
    }),
    rule: (hash: string) => ['/new-homepage'].includes(hash)
  },
  bills: {
    isFMPNode: node => {
      return (isMenu(node) && existMyBills()) || (isMyBills(node) && existMenu())
    },
    params: () => ({
      title: '我的单据FMP'
    }),
    rule: (hash: string) => ['/bills'].includes(hash)
  },
  todo: {
    isFMPNode: node => {
      return (isMenu(node) && existDxTableCell()) || (isDxTableCell(node) && existMenu())
    },
    params: () => ({
      title: '待办FMP'
    }),
    rule: (hash: string) => ['/audit/approve', '/audit/payment', '/todo/print', '/audit/carbonCopy', '/expense-manage'].includes(hash)
  }
}