import _ from 'lodash'

const originCloneDeep = _.cloneDeep

// 创建一个全局的cloneDeep缓存管理器
class CloneDeepCache {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.accessTimes = new Map()
  }

  // 生成缓存key
  generateKey(data) {
    // 使用数据的hash或者JSON.stringify的hash
    if (typeof data === 'object' && data !== null) {
      try {
        // 简化版hash，实际可以用更复杂的hash算法
        const str = JSON.stringify(data)
        return this.simpleHash(str)
      } catch (e) {
        return Math.random().toString(36)
      }
    }
    return String(data)
  }

  simpleHash(str) {
    let hash = 0
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32bit integer
    }
    return hash.toString(36)
  }

  get(data) {
    const key = this.generateKey(data)

    if (this.cache.has(key)) {
      this.accessTimes.set(key, Date.now())
      console.log('use cache')
      return this.cache.get(key)
    }

    return null
  }

  set(data, clonedData) {
    const key = this.generateKey(data)

    // 如果缓存满了，删除最久未访问的
    if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed()
    }

    this.cache.set(key, clonedData)
    this.accessTimes.set(key, Date.now())
  }

  evictLeastRecentlyUsed() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, time] of this.accessTimes) {
      if (time < oldestTime) {
        oldestTime = time
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.cache.delete(oldestKey)
      this.accessTimes.delete(oldestKey)
    }
  }

  clear() {
    this.cache.clear()
    this.accessTimes.clear()
  }
}

// 全局缓存实例
const cloneDeepCache = new CloneDeepCache(50)

// 优化后的cloneDeep函数
function optimizedCloneDeep(data) {
  // 先尝试从缓存获取
  const cached = cloneDeepCache.get(data)
  if (cached) {
    return cached
  }

  // 缓存未命中，执行真正的cloneDeep
  const cloned = originCloneDeep(data)

  // 存入缓存
  cloneDeepCache.set(data, cloned)

  return cloned
}

window.__cloneDeepCache = cloneDeepCache

_.cloneDeep = optimizedCloneDeep
