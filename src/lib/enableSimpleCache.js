/**
 * 简单缓存启用器
 * 
 * 在应用入口导入此文件即可启用 cloneDeep 缓存
 * import './lib/enableSimpleCache'
 */

// import './simpleCloneDeepCache'
import './hashPerformanceTest'

// // 🔧 开发环境的调试工具
// if (process.env.NODE_ENV === 'development') {
//   // 每30秒输出一次统计
//   setInterval(() => {
//     const stats = window.__simpleCloneDeepCache.getStats()
//     if (stats.hits + stats.misses > 0) {
//       console.log('📊 CloneDeep 缓存统计:', stats)
      
//       // 性能警告
//       if (parseFloat(stats.hitRate) < 30) {
//         console.warn('⚠️ 缓存命中率较低:', stats.hitRate)
//       }
//     }
//   }, 30000)
  
//   // 页面卸载时输出最终统计
//   window.addEventListener('beforeunload', () => {
//     const stats = window.__simpleCloneDeepCache.getStats()
//     console.log('📋 页面卸载时的缓存统计:', stats)
//   })
  
//   console.log('🚀 Simple CloneDeep Cache 已启用 (开发模式)')
//   console.log('💡 使用 _.cloneDeep.getStats() 查看统计')
// } else {
//   console.log('🚀 Simple CloneDeep Cache 已启用 (生产模式)')
// }

