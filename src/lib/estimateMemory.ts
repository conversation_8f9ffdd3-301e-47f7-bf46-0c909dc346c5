export function measureFieldMemory(fieldName, fieldValue) {
  // 测量前的内存状态
  const beforeMemory = performance.memory ? {
    used: performance.memory.usedJSHeapSize,
    total: performance.memory.totalJSHeapSize,
    limit: performance.memory.jsHeapSizeLimit
  } : null;

  // 创建字段
  const testObject = {};
  testObject[fieldName] = fieldValue;

  // 强制垃圾回收（如果可用）
  if (window.gc) {
    window.gc();
  }

  // 测量后的内存状态
  const afterMemory = performance.memory ? {
    used: performance.memory.usedJSHeapSize,
    total: performance.memory.totalJSHeapSize,
    limit: performance.memory.jsHeapSizeLimit
  } : null;

  if (beforeMemory && afterMemory) {
    const memoryDiff = afterMemory.used - beforeMemory.used;
    console.log(`字段 "${fieldName}" 大约占用内存: ${memoryDiff} bytes`);
    return memoryDiff;
  }

  return null;
}

export function getMemoryUsage() {
  return performance.memory ? {
    used: performance.memory.usedJSHeapSize,
    total: performance.memory.totalJSHeapSize,
    limit: performance.memory.jsHeapSizeLimit
  } : null;
}
