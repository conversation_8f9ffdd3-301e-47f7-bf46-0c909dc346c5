export async function measureFieldMemory(fieldName, fieldValue) {
  // 测量前的内存状态
  const beforeMemory = await getMemoryUsageAccurate()

  // 创建字段
  const testObject = {};
  testObject[fieldName] = fieldValue;

  // 强制垃圾回收（如果可用）
  if (window.gc) {
    window.gc();
  }

  // 测量后的内存状态
  const afterMemory = await getMemoryUsageAccurate()

  if (beforeMemory && afterMemory) {
    const memoryDiff = afterMemory.used - beforeMemory.used;
    console.log(`字段 "${fieldName}" 大约占用内存: ${memoryDiff} bytes`);
    return memoryDiff;
  }

  return null;
}

export function getMemoryUsageAccurate() {
  // 强制垃圾回收（如果可用）
  if (window.gc) {
    window.gc()
  }
  
  // 等待一小段时间让内存稳定
  return new Promise(resolve => {
    setTimeout(() => {
      const memory = performance.memory ? {
        used: Math.round(performance.memory.usedJSHeapSize / 1024), // KB
        total: Math.round(performance.memory.totalJSHeapSize / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024)
      } : { used: 'N/A', total: 'N/A', limit: 'N/A' };
      resolve(memory);
    }, 100);
  });
}
