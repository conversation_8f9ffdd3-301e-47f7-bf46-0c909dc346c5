/**
 * Hash算法性能测试
 */

import _ from 'lodash'

const originCloneDeep = _.cloneDeep

// 🚀 极简高效的 cloneDeep 缓存
class SimpleCloneDeepCache {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.hits = 0
    this.misses = 0
    this.collisions = 0 // 记录hash碰撞次数
  }

  // 🔑 高效的hash算法 - 避免内存崩溃
  generateKey(data) {
    try {
      const jsonStr = JSON.stringify(data)
      // 对于大数据，使用采样hash减少计算量
      return jsonStr.length > 50000 ? this.sampledHash(jsonStr) : this.fastHash(jsonStr)
    } catch (e) {
      console.error('Simple CloneDeep Cache', e)
      // 如果无法序列化，返回 null 表示不可缓存
      return null
    }
  }

  // 快速hash算法 (FNV-1a变种)
  fastHash(str) {
    let hash = 2166136261 // FNV offset basis

    for (let i = 0; i < str.length; i++) {
      hash ^= str.charCodeAt(i)
      hash = Math.imul(hash, 16777619) // FNV prime，使用Math.imul确保32位
    }

    // 转换为正数并转为36进制字符串
    return (hash >>> 0).toString(36)
  }

  // 采样hash算法 - 对于超大数据
  sampledHash(str) {
    let hash = 2166136261
    const len = str.length
    const step = Math.max(1, Math.floor(len / 1000)) // 最多采样1000个字符

    // 采样开头、中间、结尾的字符
    for (let i = 0; i < len; i += step) {
      hash ^= str.charCodeAt(i)
      hash = Math.imul(hash, 16777619)
    }

    // 加入长度信息增加唯一性
    hash ^= len
    hash = Math.imul(hash, 16777619)

    return (hash >>> 0).toString(36)
  }

  get(data) {
    const key = this.generateKey(data)

    // 如果 key 为 null，表示不可缓存，直接返回 null
    if (key === null) {
      this.misses++
      return null
    }

    if (this.cache.has(key)) {
      const cachedEntry = this.cache.get(key)

      // 简单的碰撞检测：比较数据大小
      const currentSize = JSON.stringify(data).length
      if (Math.abs(cachedEntry.dataSize - currentSize) < 100) {
        // 大小相近，认为是同一数据
        this.hits++
        return cachedEntry.clonedData
      } else {
        // 可能是hash碰撞，删除旧缓存
        this.collisions++
        this.cache.delete(key)
        this.misses++
        return null
      }
    }

    this.misses++
    return null
  }

  set(data, clonedData) {
    const key = this.generateKey(data)

    // 如果 key 为 null，表示不可缓存，直接返回
    if (key === null) {
      return
    }

    // 如果缓存满了，删除最老的一个
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    // 存储数据大小用于碰撞检测
    const dataSize = JSON.stringify(data).length
    this.cache.set(key, {
      clonedData: clonedData,
      dataSize: dataSize
    })
  }

  // 📊 简单统计
  getStats() {
    const total = this.hits + this.misses
    const hitRate = total > 0 ? ((this.hits / total) * 100).toFixed(1) : 0
    
    return {
      hits: this.hits,
      misses: this.misses,
      collisions: this.collisions,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size
    }
  }

  clear() {
    this.cache.clear()
    this.hits = 0
    this.misses = 0
    this.collisions = 0
  }
}

// 全局缓存实例
const cache = new SimpleCloneDeepCache(10)

// 🔄 优化后的 cloneDeep
function optimizedCloneDeep(data) {
  // 先尝试从缓存获取
  const cached = cache.get(data)
  if (cached) {
    return cached
  }

  // 缓存未命中，执行真正的 cloneDeep
  const cloned = originCloneDeep(data)
  
  // 存入缓存
  cache.set(data, cloned)
  
  return cloned
}

// 🔧 暴露调试接口
optimizedCloneDeep.getStats = () => cache.getStats()
optimizedCloneDeep.clear = () => cache.clear()

// 🌐 全局暴露
window.__simpleCloneDeepCache = cache

// 🔄 替换原始的 lodash cloneDeep
_.cloneDeep = optimizedCloneDeep

console.log('🚀 Simple CloneDeep Cache 已启用！使用 _.cloneDeep.getStats() 查看统计')

// 🧪 测试hash算法性能
function testHashPerformance() {
  console.log('🧪 Hash算法性能测试开始...')
  
  // 生成不同大小的测试数据
  const testCases = [
    {
      name: '小数据 (1KB)',
      data: { items: new Array(10).fill(0).map((_, i) => ({ id: i, name: `item_${i}` })) }
    },
    {
      name: '中等数据 (10KB)', 
      data: { items: new Array(100).fill(0).map((_, i) => ({ id: i, name: `item_${i}`, data: 'x'.repeat(100) })) }
    },
    {
      name: '大数据 (100KB)',
      data: { items: new Array(500).fill(0).map((_, i) => ({ id: i, name: `item_${i}`, data: 'x'.repeat(200) })) }
    },
    {
      name: '超大数据 (1MB)',
      data: { items: new Array(1000).fill(0).map((_, i) => ({ id: i, name: `item_${i}`, data: 'x'.repeat(1000) })) }
    }
  ]
  
  testCases.forEach(testCase => {
    console.log(`\n📏 测试 ${testCase.name}:`)
    
    const iterations = 100
    optimizedCloneDeep.clear()
    
    // 测试hash生成性能
    const hashStartTime = performance.now()
    for (let i = 0; i < iterations; i++) {
      JSON.stringify(testCase.data) // 模拟hash key生成
    }
    const hashEndTime = performance.now()
    const hashTime = hashEndTime - hashStartTime
    
    // 测试optimizedCloneDeep性能（无缓存）
    const cloneStartTime = performance.now()
    for (let i = 0; i < iterations; i++) {
      optimizedCloneDeep(testCase.data)
    }
    const cloneEndTime = performance.now()
    const cloneTime = cloneEndTime - cloneStartTime
    
    // 测试缓存效果
    optimizedCloneDeep.clear()
    optimizedCloneDeep(testCase.data) // 建立缓存
    
    const cachedStartTime = performance.now()
    for (let i = 0; i < iterations; i++) {
      optimizedCloneDeep(testCase.data)
    }
    const cachedEndTime = performance.now()
    const cachedTime = cachedEndTime - cachedStartTime
    
    const stats = optimizedCloneDeep.getStats()
    const improvement = ((cloneTime - cachedTime) / cloneTime * 100).toFixed(1)
    
    console.log(`  Hash生成: ${hashTime.toFixed(2)}ms (${(hashTime/iterations).toFixed(2)}ms/次)`)
    console.log(`  无缓存克隆: ${cloneTime.toFixed(2)}ms`)
    console.log(`  有缓存克隆: ${cachedTime.toFixed(2)}ms`)
    console.log(`  性能提升: ${improvement}%`)
    console.log(`  Hash开销占比: ${(hashTime/cloneTime*100).toFixed(1)}%`)
    console.log(`  缓存统计:`, stats)
    
    // 检查是否有hash碰撞
    if (stats.collisions > 0) {
      console.warn(`  ⚠️ 检测到 ${stats.collisions} 次hash碰撞`)
    }
  })
}

// 🔍 测试hash碰撞率
function testHashCollisions() {
  console.log('\n🔍 Hash碰撞测试...')
  
  optimizedCloneDeep.clear()
  
  // 生成1000个略有不同的数据
  const baseData = {
    items: new Array(100).fill(0).map((_, i) => ({ id: i, name: `item_${i}` }))
  }
  
  const variations = []
  for (let i = 0; i < 1000; i++) {
    const variation = {
      ...baseData,
      timestamp: Date.now() + i,
      random: Math.random(),
      index: i
    }
    variations.push(variation)
    optimizedCloneDeep(variation)
  }
  
  const stats = optimizedCloneDeep.getStats()
  console.log(`生成了1000个不同数据的缓存`)
  console.log(`缓存大小: ${stats.cacheSize}`)
  console.log(`碰撞次数: ${stats.collisions}`)
  console.log(`碰撞率: ${(stats.collisions/1000*100).toFixed(2)}%`)
  
  if (stats.collisions === 0) {
    console.log('✅ 无hash碰撞，算法表现良好')
  } else if (stats.collisions < 10) {
    console.log('⚠️ 少量hash碰撞，可接受范围')
  } else {
    console.warn('🚨 hash碰撞较多，建议优化算法')
  }
}

// 🎯 模拟真实业务场景
function testRealWorldScenario() {
  console.log('\n🎯 真实业务场景测试...')
  
  // 模拟attachment-fetch.js中的数据结构
  const formDetails = new Array(50).fill(0).map((_, i) => ({
    id: `detail_${i}`,
    feeTypeForm: {
      apportions: new Array(10).fill(0).map((_, j) => ({
        apportionId: `apportion_${i}_${j}`,
        apportionForm: {
          apportionMoney: { 
            standard: 1000 + j * 100,
            foreign: 800 + j * 80,
            standardStrCode: 'CNY',
            foreignStrCode: 'USD'
          },
          apportionPercent: 10 + j,
          department: { id: `dept_${j}`, name: `部门${j}` },
          project: { id: `proj_${j}`, name: `项目${j}` }
        },
        specificationId: {
          components: [
            { field: 'department', label: '部门' },
            { field: 'project', label: '项目' }
          ]
        }
      }))
    }
  }))
  
  optimizedCloneDeep.clear()
  
  console.log('模拟50个组件实例，每个调用20次...')
  
  const startTime = performance.now()
  
  // 模拟50个组件实例
  for (let component = 0; component < 50; component++) {
    // 每个组件调用20次（模拟props变化和重渲染）
    for (let call = 0; call < 20; call++) {
      optimizedCloneDeep(formDetails)
    }
  }
  
  const endTime = performance.now()
  const totalTime = endTime - startTime
  
  const stats = optimizedCloneDeep.getStats()
  
  console.log(`总耗时: ${totalTime.toFixed(2)}ms`)
  console.log(`平均每次: ${(totalTime / 1000).toFixed(2)}ms`)
  console.log(`缓存统计:`, stats)
  
  // 性能评估
  if (parseFloat(stats.hitRate) > 80) {
    console.log('✅ 缓存命中率优秀')
  } else if (parseFloat(stats.hitRate) > 50) {
    console.log('⚠️ 缓存命中率良好')
  } else {
    console.warn('🚨 缓存命中率较低，需要优化')
  }
  
  if (stats.collisions === 0) {
    console.log('✅ 无hash碰撞')
  } else {
    console.warn(`⚠️ 检测到 ${stats.collisions} 次hash碰撞`)
  }
  
  if (totalTime < 100) {
    console.log('✅ 性能优秀')
  } else if (totalTime < 500) {
    console.log('⚠️ 性能良好')
  } else {
    console.warn('🚨 性能较差，需要优化')
  }
}

// 🚀 运行所有测试
export function runHashTests() {
  console.log('🚀 开始Hash算法性能测试\n')
  
  testHashPerformance()
  testHashCollisions()
  testRealWorldScenario()
  
  console.log('\n✅ Hash测试完成！')
  console.log('💡 如果hash开销 < 10% 且碰撞率 < 1%，说明算法优秀')
}

// 如果直接运行此文件
if (typeof window !== 'undefined') {
  window.runHashTests = runHashTests
  console.log('🔧 Hash测试工具已加载，使用 runHashTests() 开始测试')
}

runHashTests()
_.cloneDeep = originCloneDeep
