import _ from 'lodash'

const originCloneDeep = _.cloneDeep

// 🚀 极简高效的 cloneDeep 缓存
class SimpleCloneDeepCache {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.hits = 0
    this.misses = 0
  }

  // 🔑 最简单的key生成 - 直接使用 JSON.stringify
  generateKey(data) {
    try {
      return JSON.stringify(data)
    } catch (e) {
      console.error('Simple CloneDeep Cache', e)
      // 如果无法序列化，返回 null 表示不可缓存
      return null
    }
  }

  get(data) {
    const key = this.generateKey(data)

    // 如果 key 为 null，表示不可缓存，直接返回 null
    if (key === null) {
      this.misses++
      return null
    }

    if (this.cache.has(key)) {
      this.hits++
      return this.cache.get(key)
    }

    this.misses++
    return null
  }

  set(data, clonedData) {
    const key = this.generateKey(data)

    // 如果 key 为 null，表示不可缓存，直接返回
    if (key === null) {
      return
    }

    // 如果缓存满了，删除最老的一个
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    this.cache.set(key, clonedData)
  }

  // 📊 简单统计
  getStats() {
    const total = this.hits + this.misses
    const hitRate = total > 0 ? ((this.hits / total) * 100).toFixed(1) : 0
    
    return {
      hits: this.hits,
      misses: this.misses,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size
    }
  }

  clear() {
    this.cache.clear()
    this.hits = 0
    this.misses = 0
  }
}

// 全局缓存实例
const cache = new SimpleCloneDeepCache(100)

// 🔄 优化后的 cloneDeep
function optimizedCloneDeep(data) {
  // 先尝试从缓存获取
  const cached = cache.get(data)
  if (cached) {
    return cached
  }

  // 缓存未命中，执行真正的 cloneDeep
  const cloned = originCloneDeep(data)
  
  // 存入缓存
  cache.set(data, cloned)
  
  return cloned
}

// 🔧 暴露调试接口
optimizedCloneDeep.getStats = () => cache.getStats()
optimizedCloneDeep.clear = () => cache.clear()

// 🌐 全局暴露
window.__simpleCloneDeepCache = cache

// 🔄 替换原始的 lodash cloneDeep
_.cloneDeep = optimizedCloneDeep

console.log('🚀 Simple CloneDeep Cache 已启用！使用 _.cloneDeep.getStats() 查看统计')
