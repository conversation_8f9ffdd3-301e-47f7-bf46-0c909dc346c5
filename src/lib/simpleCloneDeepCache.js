import _ from 'lodash'

const originCloneDeep = _.cloneDeep

// 🚀 极简高效的 cloneDeep 缓存
class SimpleCloneDeepCache {
  constructor(maxSize = 100) {
    this.cache = new Map()
    this.maxSize = maxSize
    this.hits = 0
    this.misses = 0
    this.collisions = 0 // 记录hash碰撞次数
  }

  // 🔑 高效的hash算法 - 避免内存崩溃
  generateKey(data) {
    try {
      const jsonStr = JSON.stringify(data)
      // 对于大数据，使用采样hash减少计算量
      return jsonStr.length > 50000 ? this.sampledHash(jsonStr) : this.fastHash(jsonStr)
    } catch (e) {
      console.error('Simple CloneDeep Cache', e)
      // 如果无法序列化，返回 null 表示不可缓存
      return null
    }
  }

  // 快速hash算法 (FNV-1a变种)
  fastHash(str) {
    let hash = 2166136261 // FNV offset basis

    for (let i = 0; i < str.length; i++) {
      hash ^= str.charCodeAt(i)
      hash = Math.imul(hash, 16777619) // FNV prime，使用Math.imul确保32位
    }

    // 转换为正数并转为36进制字符串
    return (hash >>> 0).toString(36)
  }

  // 采样hash算法 - 对于超大数据
  sampledHash(str) {
    let hash = 2166136261
    const len = str.length
    const step = Math.max(1, Math.floor(len / 1000)) // 最多采样1000个字符

    // 采样开头、中间、结尾的字符
    for (let i = 0; i < len; i += step) {
      hash ^= str.charCodeAt(i)
      hash = Math.imul(hash, 16777619)
    }

    // 加入长度信息增加唯一性
    hash ^= len
    hash = Math.imul(hash, 16777619)

    return (hash >>> 0).toString(36)
  }

  get(data) {
    const key = this.generateKey(data)

    // 如果 key 为 null，表示不可缓存，直接返回 null
    if (key === null) {
      this.misses++
      return null
    }

    if (this.cache.has(key)) {
      const cachedEntry = this.cache.get(key)

      // 简单的碰撞检测：比较数据大小
      const currentSize = JSON.stringify(data).length
      if (Math.abs(cachedEntry.dataSize - currentSize) < 100) {
        // 大小相近，认为是同一数据
        this.hits++
        return cachedEntry.clonedData
      } else {
        // 可能是hash碰撞，删除旧缓存
        this.collisions++
        this.cache.delete(key)
        this.misses++
        return null
      }
    }

    this.misses++
    return null
  }

  set(data, clonedData) {
    const key = this.generateKey(data)

    // 如果 key 为 null，表示不可缓存，直接返回
    if (key === null) {
      return
    }

    // 如果缓存满了，删除最老的一个
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value
      this.cache.delete(firstKey)
    }

    // 存储数据大小用于碰撞检测
    const dataSize = JSON.stringify(data).length
    this.cache.set(key, {
      clonedData: clonedData,
      dataSize: dataSize
    })
  }

  // 📊 简单统计
  getStats() {
    const total = this.hits + this.misses
    const hitRate = total > 0 ? ((this.hits / total) * 100).toFixed(1) : 0
    
    return {
      hits: this.hits,
      misses: this.misses,
      collisions: this.collisions,
      hitRate: `${hitRate}%`,
      cacheSize: this.cache.size
    }
  }

  clear() {
    this.cache.clear()
    this.hits = 0
    this.misses = 0
    this.collisions = 0
  }
}

// 全局缓存实例
const cache = new SimpleCloneDeepCache(10)

// 🔄 优化后的 cloneDeep
function optimizedCloneDeep(data) {
  // 先尝试从缓存获取
  const cached = cache.get(data)
  if (cached) {
    return cached
  }

  // 缓存未命中，执行真正的 cloneDeep
  const cloned = originCloneDeep(data)
  
  // 存入缓存
  cache.set(data, cloned)
  
  return cloned
}

// 🔧 暴露调试接口
optimizedCloneDeep.getStats = () => cache.getStats()
optimizedCloneDeep.clear = () => cache.clear()

// 🌐 全局暴露
window.__simpleCloneDeepCache = cache

// 🔄 替换原始的 lodash cloneDeep
_.cloneDeep = optimizedCloneDeep

console.log('🚀 Simple CloneDeep Cache 已启用！使用 _.cloneDeep.getStats() 查看统计')
