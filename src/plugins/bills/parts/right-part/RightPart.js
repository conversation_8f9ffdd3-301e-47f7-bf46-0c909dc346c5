/**************************************************
 * Created by nany<PERSON><PERSON><PERSON> on 10/07/2017 17:41.
 **************************************************/
import React, { PureComponent } from 'react'
import RightPartFooter from './billInfo/RightPartFooter'
import BillInfoPart from './billInfo/BillInfoPart'
import { EnhanceConnect } from '@ekuaibao/store'
import { cloneDeep, get } from 'lodash'
import {
  getFlowInfoById,
  setValidateError,
  getBillHistoryVersionList,
  getBillHistoryVersionDetail
} from '../../bills.action'
import { app as api, app } from '@ekuaibao/whispered'
import { showLoading, hideLoading } from '@ekuaibao/lib/lib/lib-util'
import { trackBillReviewTime } from '../../util/trackBill'
import { getDiffsBetweenVersions, getRiskReasonDataForVersionDiffModal } from '../../util/billUtils'
import { BillVersionDiffModal } from '../../layers/bill-version-diff/bill-version-diff-modal'
import { BillAdditionalMessageApi } from '@ekuaibao/ekuaibao_types'
import Content from './../../layers/add-bill-fullscreen/views/content'
import { fnIsRiskError } from '../../riskWarning/formatRiskWarningData'
import { SkeletonComponent } from './billInfo/SkeletonModal'
import { logEvent } from '../../../../lib/logs'
const { startLoadFlowPerformanceStatistics } = api.require('@lib/flowPerformanceStatistics')

@EnhanceConnect(null, { getFlowInfoById, setValidateError })
export default class RightPart extends PureComponent {
  state = {
    dataSource: void 0,
    delegator: void 0,
    showEmptyPage: true,
    historyVersions: [],
    hasDocumentType: false,
    isLoading: false,
    loadingError: false,
    pendingCopy: false,
  }

  __FIRST_TIME = 0

  retryFn = null
  retryFnWrapper = fn => {
    const retryFn = async (...args) => {
      this.retryFn = () => {
        return retryFn(...args)
      }
      fn.call(this, ...args)
    }
    return retryFn
  }

  componentDidMount() {
    let { bus, from, line } = this.props
    if (from && from === 'from_drawer') {
      this.handleListLineClick(line)
    }
    bus.on('list:line:click', this.handleListLineClick)
    bus.watch('bills:update:flow', this.handleBillsUpdate)
    bus.watch('bills:view:diff', this.handleViewDiff)
    bus.watch('bills:check:nulllify:rule', this.handleCheckNulllify)
  }

  componentWillUnmount() {
    let { bus } = this.props
    bus.un('list:line:click', this.handleListLineClick)
    bus.un('bills:update:flow', this.handleBillsUpdate)
    bus.un('bills:view:diff', this.handleViewDiff)
    bus.un('bills:check:nulllify:rule', this.handleCheckNulllify)
  }

  // 获取小组件是否展示
  async fetchWidgetStatus(data) {
    this.setState({ showWidget: false })
    const isUsePower = (app.getState('@common').powers?.powerCodeMap || [])?.includes('160013')
    const specId = data.form.specificationId?.originalId?.id
    if (!isUsePower || !specId) return
    const config = await BillAdditionalMessageApi.fetchUserConfig(specId)
    this.setState({
      showWidget: !!config
    })
  }

  handleBillsUpdate = id => {
    this.retryFn = () => this.getBillsFlowInfoById(id)
    return this.getBillsFlowInfoById(id)
  }
  handleCheckNulllify = params => {
    if (!params) {
      this.setState({ canNullify: { done: false, value: false } })
      return
    }
    const { id, state } = params
    const NullifyPower = api.getState('@common').powers.KA_DOCUMENT_VOID
    if (NullifyPower && state && state !== 'new') {
      return api.invokeService('@bills:get:check:nulllify:rule', { specId: id, state }).then(data => {
        this.setState({ canNullify: { done: true, value: data.value } })
      })
    } else {
      this.setState({ canNullify: { done: true, value: false } })
    }
  }
  handleListLineClick = this.retryFnWrapper(line => {
    this.fnTrackViewBillTime()
    if (this.state.pendingCopy) {
      this.getBillsFlowInfoById(line)
      return
    }
    this.setState(
      {
        dataSource: null,
        riskData: {},
        showEmptyPage: !line,
        historyVersions: [],
        hasDocumentType: false,
        loadingError: false,
        isLoading: true
      },
      () => {
        //
        if (!line) return
        startLoadFlowPerformanceStatistics()
        if (line.state === 'new') {
          //申请事项中补充申请时，原申请单的值赋给新申请单
          const isFromApplyReq =
            line.formType === 'requisition' && line.requisitionInfo && !line.requisitionInfo.withNotes

          if (isFromApplyReq) {
            return this.getBillsFlowInfoByIdFromApply(line)
          } else {
            const dataSource = line?.requisitionInfo?.isQuickExpends ? line : cloneDeep(line)
            return this.setState({ page: 'BillInfo', dataSource, riskData: {}, isLoading: false })
          }
        }
        this.getBillsFlowInfoById(line)
      }
    )
  })

  getBillsFlowInfoByIdFromApply = line => {
    let { getFlowInfoById } = this.props
    const id = get(line, 'requisitionInfo.requisition.flowId.id')
    // 通过申请单id查询到该申请单的详情 所以会包含该申请单的所有的信息(包含行程)
    return getFlowInfoById({ id }).then(action => {
      if (action.error) throw new Error(action?.payload)
      let data = this.formatValueForApplyValue(action.payload.value)
      data.form.specificationId = line?.requisitionInfo?.defaultSpecification
      data.requisitionInfo = line.requisitionInfo
      // 申请单的行程 费用明细 不需要关联
      data.form['u_行程规划'] = []
      data.form['details'] = []
      if (data.form?.travelPlanning?.length) {
        data.form.travelPlanning?.forEach(travel => {
          if (travel?.travelId) delete travel.travelId
        })
      }
      this.setState({
        page: 'BillInfo',
        dataSource: data,
        riskData: {},
        isLoading: false,
        loadingError: false
      })
    }).catch(e => {
      this.setState({
        isLoading: false,
        loadingError: true
      })
    })
  }

  formatValueForApplyValue = value => {
    let data = cloneDeep(value)
    const {
      form: { details, trips }
    } = data
    details?.forEach(v => {
      const { feeTypeForm = {} } = v || {}
      delete feeTypeForm.detailId
    })
    trips?.forEach(v => {
      const { tripForm = {} } = v || {}
      delete tripForm.tripId
    })
    delete data.id
    delete data.code
    data.logs = []
    data.plan = null
    data.state = 'new'
    return data
  }

  getBillsFlowInfoById = async line => {
    let { getFlowInfoById, bus } = this.props
    const { id } = line
    let isForbid = line.state === 'rejected' || line.state === 'draft'
    const level = isForbid ? 'OutOfLimitReject' : ''
    try {
      // 确保先获取 `dataSource`
      const action = await getFlowInfoById({ id })
      if (action.error) throw new Error(action?.payload)
      // let data = cloneDeep(action.payload.value)
      let data = action.payload.value

      this.setState(
        {
          page: 'BillInfo',
          dataSource: data,
          isLoading: false,
          loadingError: false
        },
        () => {
          //草稿态点击复制：先存为草稿，刷新后复制草稿单据
          bus.emit('footer:notifycheck:pending:copy')
        }
      )
      showLoading()
      api
        .invokeService('@bills:get:flow:risk:warning', level ? { id, level } : { id })
        .then(riskData => {
          isForbid = isForbid && fnIsRiskError(riskData?.value?.riskWarning)
          riskData.isForbid = isForbid
          hideLoading()
          this.setState({ riskData, showEmptyPage: false })
        })
        .catch(() => {
          hideLoading()
        })
      this.getHistoryVersions(data)
      this.fetchWidgetStatus(data)

      return data
    } catch (e) {
      logEvent('获取单据详情失败', e)
      this.setState({
        loadingError: true,
        isLoading: false
      })
    }
  }

  fnTrackViewBillTime = () => {
    if (!!this.__FIRST_TIME) {
      trackBillReviewTime({ startTime: this.__FIRST_TIME, endTime: Date.now() })
    }
    this.__FIRST_TIME = Date.now()
    window.__LOAD_FLOW_TIME = Date.now() //加载单据开始时间
  }

  getDiffs = async (type, curId, prevId) => {
    const privilegeId = get(this.props, 'privilegeId')
    const curVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    const prevVersion = await getBillHistoryVersionDetail(prevId, privilegeId)
    const diffs = await getDiffsBetweenVersions(type, curVersion, prevVersion)
    return diffs
  }

  CheckHasDocumentType = async curId => {
    if (!curId) {
      return false
    }
    const privilegeId = get(this.props, 'privilegeId')
    const firstVersion = await getBillHistoryVersionDetail(curId, privilegeId)
    return !!firstVersion?.value?.form?.details
  }

  getHistoryVersions = async data => {
    const privilegeId = get(this.props, 'privilegeId')
    const res = await getBillHistoryVersionList(data?.id, privilegeId)
    const hasDocumentType = await this.CheckHasDocumentType(res.items?.[0]?.id)
    this.setState({
      historyVersions: res.items,
      hasDocumentType
    })
  }

  componentDidUpdate(prevProps, prevState, snapshot) {
    const { props, state } = this
    if (state['dataSource'] !== prevState['dataSource']) {
      this.hideViewDiffModal()
    }
  }

  handleViewDiff = async e => {
    this.showViewDiffModal(e)
  }

  /**
   *
   * @param e {React.MouseEvent}
   */
  showViewDiffModal = e => {
    this.setState({
      isShowDiffModal: true,
      initPosition: {
        x: e.clientX - 375,
        y: e.clientY
      }
    })
    this.forceUpdate()
  }

  hideViewDiffModal = () => {
    this.setState({
      isShowDiffModal: false,
      initPosition: undefined
    })
    this.forceUpdate()
  }

  retryWhenError = async () => {
    return this.retryFn && (await this.retryFn())
  }

  handlePengdingCopy = (bool) => {
    this.setState({
      pendingCopy: bool
    })
  }

  render() {
    let {
      bus,
      setValidateError,
      className = '',
      from = '',
      closeDrawer,
      handleCreatePopupSave,
      openFrom = '',
      flowPlanConfigId = '',
      creatStyle = '',
      viewSetting,
      callback,
      onCloseModal,
      onSetting,
      showFullScreenDrawer,
      showHeaderClose,
      layer,
      scene,
      privilegedId,
      showUpDown,
    } = this.props
    let {
      dataSource,
      riskData,
      showEmptyPage,
      historyVersions,
      canNullify,
      isShowDiffModal = false,
      initPosition,
      showWidget,
      isLoading,
      loadingError
    } = this.state
    let ds = dataSource
    if (dataSource) {
      if (openFrom && flowPlanConfigId) {
        ds = { ...dataSource, openFrom, flowPlanConfigId }
      }
    }
    if (creatStyle === 'new_modal_style') {
      return (
        <>
          <Content
            bus={bus}
            dataSource={dataSource}
            viewSetting={viewSetting}
            riskData={riskData}
            layer={layer}
            showEmptyPage={showEmptyPage}
          />
          <RightPartFooter
            onCloseModal={onCloseModal}
            onSetting={onSetting}
            creatStyle="new_modal_style"
            setValidateError={setValidateError}
            bus={bus}
            dataSource={dataSource}
            callback={callback}
            showEmptyPage={showEmptyPage}
            isLoading={isLoading}
            scene={scene}
            privilegedId={privilegedId}
          />
        </>
      )
    }
    return (
      <div className={`flex-1 dis-f fd-c w-100b ${className}`}>
        {isLoading && !showEmptyPage ? (
          <SkeletonComponent showHead={true} showSkeletonNormalList={true} lineCount={11} />
        ) : (
          <BillInfoPart
            bus={bus}
            dataSource={dataSource}
            riskData={riskData}
            layer={layer}
            mode={(from === 'from_drawer' || showHeaderClose) ? 'table' : 'list'}
            showEmptyPage={showEmptyPage}
            loadingError={loadingError}
            showWidget={showWidget}
            showHeaderClose={showHeaderClose}
            showFullScreenDrawer={showFullScreenDrawer}
            retryWhenError={this.retryWhenError.bind(this)}
            showUpDown={showUpDown}
            billFooter={
              <RightPartFooter
                from={from}
                closeDrawer={closeDrawer}
                bus={bus}
                dataSource={ds}
                scanViewData={openFrom === 'flow-preview' ? { fn: closeDrawer } : undefined}
                setValidateError={setValidateError}
                canNullify={canNullify}
                handleCreatePopupSave={handleCreatePopupSave}
                showEmptyPage={showEmptyPage}
                isLoading={isLoading}
                scene={scene}
                privilegedId={privilegedId}
                onPengdingCopy={this.handlePengdingCopy}
              />
            }
          />
        )}
        <BillVersionDiffModal
          visible={isShowDiffModal}
          versions={historyVersions}
          initPosition={initPosition}
          riskData={getRiskReasonDataForVersionDiffModal(riskData)}
          dataSource={dataSource?.form}
          getDiffs={this.getDiffs}
          onClose={this.hideViewDiffModal}
        />
      </div>
    )
  }
}
